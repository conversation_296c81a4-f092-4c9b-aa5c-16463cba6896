"""
AI Chat Module for MIT CVD App
Handles health-focused conversations and provides insights using OpenAI GPT-4
"""
import openai
import json
from typing import Dict, Any, Optional, List, Union
from datetime import datetime

from config import OPENAI_API_KEY
from data_models import current_timestamp
import crud_module

# Initialize OpenAI client
if OPENAI_API_KEY:
    try:
        from openai import OpenAI
        client = OpenAI(api_key=OPENAI_API_KEY)
        OPENAI_CLIENT_AVAILABLE = True
    except ImportError:
        openai.api_key = OPENAI_API_KEY
        OPENAI_CLIENT_AVAILABLE = False
else:
    OPENAI_CLIENT_AVAILABLE = False

class HealthChatEngine:
    """AI-powered health conversation engine focused on CVD risk assessment"""
    
    def __init__(self):
        self.model = "gpt-4"
        self.max_tokens = 800
        self.temperature = 0.3
        
        # Health-focused system prompt
        self.system_prompt = """
        You are a cardiovascular health assistant AI. Your role is to:
        
        1. Provide helpful, evidence-based health information
        2. Identify potential cardiovascular risk factors from user input
        3. Suggest lifestyle improvements for heart health
        4. Encourage users to consult healthcare professionals for medical advice
        5. Never provide specific medical diagnoses or treatment recommendations
        
        Always respond with empathy and encourage healthy lifestyle choices.
        Focus on cardiovascular health, nutrition, exercise, stress management, and sleep.
        
        IMPORTANT: Always remind users that this is not medical advice and they should 
        consult healthcare professionals for medical concerns.
        """
    
    def get_insights(self, user_input: Union[str, Dict[str, Any]], user_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Generate health insights from user input
        
        Args:
            user_input: Text input or dict with audio transcript
            user_context: User profile data for personalized responses
            
        Returns:
            Dict with AI response, insights, and risk factors
        """
        try:
            # Extract text from input
            if isinstance(user_input, dict):
                if user_input.get('type') == 'audio_transcript':
                    text_input = user_input.get('content', '')
                    input_type = 'audio'
                else:
                    text_input = str(user_input)
                    input_type = 'text'
            else:
                text_input = str(user_input)
                input_type = 'text'
            
            if not text_input.strip():
                return self._create_error_response("No input provided")
            
            # Build context-aware prompt
            prompt = self._build_personalized_prompt(text_input, user_context, input_type)
            
            # Get AI response
            if OPENAI_CLIENT_AVAILABLE or OPENAI_API_KEY:
                ai_response = self._get_ai_response(prompt)
            else:
                return self._create_mock_response(text_input, user_context)
            
            if not ai_response['success']:
                return ai_response
            
            # Extract insights from response
            insights = self._extract_insights(ai_response['content'], text_input)
            
            # Log conversation if user provided
            if user_context and user_context.get('id'):
                self._log_conversation(user_context['id'], text_input, ai_response['content'], insights)
            
            return {
                'success': True,
                'response': ai_response['content'],
                'insights': insights,
                'input_type': input_type,
                'timestamp': current_timestamp()
            }
            
        except Exception as e:
            return self._create_error_response(f"Chat processing error: {str(e)}")
    
    def _build_personalized_prompt(self, text_input: str, user_context: Optional[Dict[str, Any]], input_type: str) -> str:
        """Build personalized prompt based on user context"""
        prompt = f"{self.system_prompt}\n\n"
        
        # Add user context if available
        if user_context:
            prompt += "User Context:\n"
            prompt += f"- Age: {user_context.get('age', 'Unknown')}\n"
            
            profile = user_context.get('profile', {})
            lifestyle = profile.get('lifestyle', {})
            
            if lifestyle:
                prompt += f"- Exercise frequency: {lifestyle.get('exercise_frequency', 0)} times/week\n"
                prompt += f"- Smoking status: {'Smoker' if lifestyle.get('smoking') else 'Non-smoker'}\n"
                prompt += f"- Stress level: {lifestyle.get('stress_level', 5)}/10\n"
                prompt += f"- Sleep hours: {lifestyle.get('sleep_hours', 8)} hours/night\n"
            
            family_history = profile.get('family_history', {})
            if family_history:
                prompt += f"- Family history: {', '.join([k for k, v in family_history.items() if v])}\n"
            
            # Add recent CVD scores if available
            cvd_scores = user_context.get('cvd_scores', [])
            if cvd_scores:
                latest_score = cvd_scores[-1]
                prompt += f"- Latest CVD risk: {latest_score.get('risk_level', 'Unknown')} ({latest_score.get('score', 0):.2f})\n"
        
        prompt += f"\nInput type: {input_type}\n"
        prompt += f"User message: {text_input}\n\n"
        prompt += "Please provide a helpful response and identify any cardiovascular risk factors or health insights."
        
        return prompt
    
    def _get_ai_response(self, prompt: str) -> Dict[str, Any]:
        """Get response from OpenAI API"""
        try:
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": prompt}
            ]
            
            if OPENAI_CLIENT_AVAILABLE:
                response = client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                    max_tokens=self.max_tokens,
                    temperature=self.temperature
                )
                content = response.choices[0].message.content
            else:
                response = openai.ChatCompletion.create(
                    model=self.model,
                    messages=messages,
                    max_tokens=self.max_tokens,
                    temperature=self.temperature
                )
                content = response.choices[0].message.content
            
            return {
                'success': True,
                'content': content
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"OpenAI API error: {str(e)}"
            }
    
    def _extract_insights(self, ai_response: str, user_input: str) -> Dict[str, Any]:
        """Extract structured insights from AI response"""
        insights = {
            'risk_factors': [],
            'recommendations': [],
            'health_keywords': [],
            'urgency_level': 'low'
        }
        
        # Extract risk factors (simple keyword matching)
        risk_keywords = [
            'chest pain', 'shortness of breath', 'high blood pressure', 'cholesterol',
            'diabetes', 'smoking', 'family history', 'stress', 'sedentary',
            'overweight', 'obesity', 'heart palpitations', 'fatigue'
        ]
        
        response_lower = ai_response.lower()
        input_lower = user_input.lower()
        
        for keyword in risk_keywords:
            if keyword in input_lower or keyword in response_lower:
                insights['health_keywords'].append(keyword)
                if keyword in ['chest pain', 'shortness of breath', 'heart palpitations']:
                    insights['risk_factors'].append(f"Reported {keyword}")
                    insights['urgency_level'] = 'high'
                elif keyword in ['high blood pressure', 'diabetes', 'smoking']:
                    insights['risk_factors'].append(f"Potential {keyword} concern")
                    if insights['urgency_level'] == 'low':
                        insights['urgency_level'] = 'medium'
        
        # Extract recommendations (simple pattern matching)
        if 'exercise' in response_lower or 'physical activity' in response_lower:
            insights['recommendations'].append("Increase physical activity")
        
        if 'diet' in response_lower or 'nutrition' in response_lower:
            insights['recommendations'].append("Improve dietary habits")
        
        if 'stress' in response_lower:
            insights['recommendations'].append("Manage stress levels")
        
        if 'sleep' in response_lower:
            insights['recommendations'].append("Optimize sleep quality")
        
        if 'doctor' in response_lower or 'healthcare' in response_lower:
            insights['recommendations'].append("Consult healthcare provider")
        
        return insights
    
    def _log_conversation(self, user_id: str, user_input: str, ai_response: str, insights: Dict[str, Any]) -> None:
        """Log conversation to user's chat history"""
        try:
            # Create or get session
            session = crud_module.create_session(user_id, 'chat')
            
            if session:
                # Add message to session
                message = {
                    'timestamp': current_timestamp(),
                    'user_input': user_input,
                    'ai_response': ai_response,
                    'insights': insights
                }
                
                session_data = crud_module.read_session(session['id'])
                if session_data:
                    session_data['messages'].append(message)
                    session_data['insights'].extend(insights.get('risk_factors', []))
                    crud_module.update_session(session['id'], session_data)
        
        except Exception as e:
            print(f"Warning: Could not log conversation: {e}")
    
    def _create_mock_response(self, text_input: str, user_context: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Create mock response when OpenAI API is not available"""
        mock_responses = [
            "Thank you for sharing your health concern. Based on general health guidelines, I recommend maintaining a balanced diet, regular exercise, and consulting with a healthcare provider for personalized advice.",
            "I understand your concern about cardiovascular health. It's important to monitor risk factors like blood pressure, cholesterol, and maintain healthy lifestyle habits. Please consult a healthcare professional for proper evaluation.",
            "Your health question is important. While I can provide general information, it's best to discuss specific symptoms or concerns with a qualified healthcare provider who can give you personalized medical advice."
        ]
        
        # Simple response selection based on input
        response_index = len(text_input) % len(mock_responses)
        mock_response = mock_responses[response_index]
        
        # Add user context if available
        if user_context:
            age = user_context.get('age', 0)
            if age > 50:
                mock_response += " Given your age, regular cardiovascular check-ups are particularly important."
        
        mock_insights = {
            'risk_factors': [],
            'recommendations': ['Consult healthcare provider', 'Maintain healthy lifestyle'],
            'health_keywords': [],
            'urgency_level': 'low'
        }
        
        # Simple keyword detection for mock insights
        if any(word in text_input.lower() for word in ['chest pain', 'heart', 'pressure']):
            mock_insights['risk_factors'].append('Cardiovascular concern mentioned')
            mock_insights['urgency_level'] = 'medium'
        
        return {
            'success': True,
            'response': mock_response,
            'insights': mock_insights,
            'input_type': 'text',
            'timestamp': current_timestamp(),
            'mock': True
        }
    
    def _create_error_response(self, error_message: str) -> Dict[str, Any]:
        """Create standardized error response"""
        return {
            'success': False,
            'error': error_message,
            'response': '',
            'insights': {'risk_factors': [], 'recommendations': []},
            'timestamp': current_timestamp()
        }

# Convenience functions
def get_insights(user_input: Union[str, Dict[str, Any]], user_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """Main chat function - wrapper for the class method"""
    engine = HealthChatEngine()
    return engine.get_insights(user_input, user_context)

def quick_health_check(symptoms: str) -> str:
    """Quick health check that returns just the response text"""
    result = get_insights(symptoms)
    return result.get('response', 'Unable to process health check') if result.get('success') else 'Health check failed'

def analyze_health_conversation(conversation_history: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze a conversation history for health patterns"""
    all_risk_factors = []
    all_recommendations = []

    for message in conversation_history:
        insights = message.get('insights', {})
        all_risk_factors.extend(insights.get('risk_factors', []))
        all_recommendations.extend(insights.get('recommendations', []))

    # Remove duplicates while preserving order
    unique_risk_factors = list(dict.fromkeys(all_risk_factors))
    unique_recommendations = list(dict.fromkeys(all_recommendations))

    return {
        'total_messages': len(conversation_history),
        'risk_factors': unique_risk_factors,
        'recommendations': unique_recommendations,
        'risk_level': 'high' if len(unique_risk_factors) > 3 else 'medium' if len(unique_risk_factors) > 1 else 'low'
    }

"""
Setup script for MIT CVD App
Quick installation and configuration
"""
import os
import subprocess
import sys

def install_dependencies():
    """Install required Python packages"""
    print("📦 Installing dependencies...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def setup_directories():
    """Create necessary directories"""
    print("📁 Setting up directories...")
    
    directories = ['data', 'reports', 'temp']
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ Created directory: {directory}")

def setup_environment():
    """Setup environment configuration"""
    print("⚙️ Setting up environment...")
    
    if not os.path.exists('.env'):
        # Copy example env file
        if os.path.exists('.env.example'):
            import shutil
            shutil.copy('.env.example', '.env')
            print("✅ Created .env file from template")
            print("⚠️ Please edit .env file and add your OpenAI API key")
        else:
            # Create basic .env file
            with open('.env', 'w') as f:
                f.write("# OpenAI API Configuration\n")
                f.write("OPENAI_API_KEY=your_openai_api_key_here\n")
                f.write("\n# App Configuration\n")
                f.write("APP_NAME=MIT_CVD_APP\n")
                f.write("DEBUG=True\n")
            print("✅ Created basic .env file")
            print("⚠️ Please edit .env file and add your OpenAI API key")
    else:
        print("✅ .env file already exists")

def test_installation():
    """Test if installation is working"""
    print("🧪 Testing installation...")
    
    try:
        # Test imports
        import crud_module
        import chat_module
        import audio_module
        import vision_module
        import cvd_score_module
        import heuristic_module
        import gamification_module
        import pdf_module
        
        print("✅ All modules imported successfully")
        
        # Test basic functionality
        test_user = crud_module.create_user("Test User", 30, "<EMAIL>")
        if test_user:
            print("✅ Database operations working")
            
            # Clean up test user
            crud_module.delete_user(test_user['id'])
        
        return True
        
    except Exception as e:
        print(f"❌ Installation test failed: {e}")
        return False

def main():
    """Main setup function"""
    print("🏥 MIT CVD App Setup")
    print("=" * 30)
    
    # Setup directories
    setup_directories()
    
    # Install dependencies
    if not install_dependencies():
        print("❌ Setup failed during dependency installation")
        return
    
    # Setup environment
    setup_environment()
    
    # Test installation
    if test_installation():
        print("\n🎉 Setup completed successfully!")
        print("\nNext steps:")
        print("1. Edit .env file and add your OpenAI API key")
        print("2. Run: python main.py demo")
        print("3. Or run: python main.py (for interactive mode)")
    else:
        print("\n❌ Setup completed with errors")
        print("Please check the error messages above")

if __name__ == "__main__":
    main()
# Core dependencies for MIT CVD App MVP
openai>=1.0.0
requests>=2.31.0
python-dotenv>=1.0.0

# API Server
fastapi>=0.104.0
uvicorn>=0.24.0
python-multipart>=0.0.6

# PDF generation
reportlab>=4.0.0
fpdf2>=2.7.0

# Audio processing
pydub>=0.25.1

# Image processing
Pillow>=10.0.0

# Data handling
pandas>=2.0.0
numpy>=1.24.0

# Date/time utilities
python-dateutil>=2.8.0

# Optional: Local Whisper for offline transcription (recommended)
# Uncomment the line below to enable local Whisper transcription
# openai-whisper>=20231117

# Optional: For enhanced audio processing
# whisper-openai>=20231117

# Development/Testing (optional)
pytest>=7.4.0
black>=23.0.0

"""
Computer Vision Food Analysis Module for MIT CVD App
Analyzes food images and provides nutritional information for CVD risk assessment
"""
import openai
import base64
import os
from typing import Dict, Any, Optional, List
from PIL import Image
import json

from config import OPENAI_API_KEY
from data_models import current_timestamp, NutritionLogModel

# Initialize OpenAI client
if OPENAI_API_KEY:
    try:
        from openai import OpenAI
        client = OpenAI(api_key=OPENAI_API_KEY)
        OPENAI_CLIENT_AVAILABLE = True
    except ImportError:
        openai.api_key = OPENAI_API_KEY
        OPENAI_CLIENT_AVAILABLE = False
else:
    OPENAI_CLIENT_AVAILABLE = False

class FoodVisionAnalyzer:
    """Food image analysis using OpenAI Vision API"""
    
    def __init__(self):
        self.supported_formats = ['.jpg', '.jpeg', '.png', '.webp', '.gif']
        self.max_image_size = 20 * 1024 * 1024  # 20MB limit
        
        # CVD-focused nutrition analysis prompt
        self.nutrition_prompt = """
        Analyze this food image for cardiovascular health assessment. Provide:
        
        1. Food identification (name and main ingredients)
        2. Estimated nutritional content per serving:
           - Calories
           - Saturated fat (g)
           - Trans fat (g) 
           - Sodium (mg)
           - Fiber (g)
           - Sugar (g)
           - Protein (g)
        3. Heart health score (1-10, where 10 is most heart-healthy)
        4. CVD risk factors present
        5. Heart-healthy alternatives or modifications
        
        Format response as JSON with clear structure.
        """
    
    def analyze_food(self, image_path: str, user_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Analyze food image for nutritional content and CVD risk assessment
        
        Args:
            image_path: Path to food image
            user_context: User profile for personalized recommendations
            
        Returns:
            Dict with food analysis, nutrition data, and CVD insights
        """
        try:
            # Validate image
            validation_result = self._validate_image(image_path)
            if not validation_result['valid']:
                return self._create_error_response(validation_result['error'])
            
            # Process image
            processed_image_path = self._prepare_image(image_path)
            
            # Analyze with OpenAI Vision
            analysis_result = self._analyze_with_vision_api(processed_image_path, user_context)
            
            # Clean up temporary file if created
            if processed_image_path != image_path:
                os.remove(processed_image_path)
            
            if analysis_result['success']:
                # Extract structured nutrition data
                nutrition_data = self._extract_nutrition_data(analysis_result['raw_response'])
                
                # Calculate CVD impact
                cvd_impact = self._calculate_cvd_impact(nutrition_data)
                
                return {
                    'success': True,
                    'food_name': nutrition_data.get('food_name', 'Unknown food'),
                    'nutrition': nutrition_data.get('nutrition', {}),
                    'heart_health_score': nutrition_data.get('heart_health_score', 5),
                    'cvd_risk_factors': nutrition_data.get('cvd_risk_factors', []),
                    'recommendations': nutrition_data.get('recommendations', []),
                    'cvd_impact': cvd_impact,
                    'image_path': image_path,
                    'analysis_timestamp': current_timestamp(),
                    'raw_response': analysis_result['raw_response']
                }
            else:
                return analysis_result
                
        except Exception as e:
            return self._create_error_response(f"Food analysis error: {str(e)}")
    
    def analyze_meal_plan(self, image_paths: List[str]) -> Dict[str, Any]:
        """Analyze multiple food images as a meal plan"""
        meal_analyses = []
        total_nutrition = {}
        
        for image_path in image_paths:
            analysis = self.analyze_food(image_path)
            if analysis['success']:
                meal_analyses.append(analysis)
                
                # Aggregate nutrition
                nutrition = analysis.get('nutrition', {})
                for key, value in nutrition.items():
                    if isinstance(value, (int, float)):
                        total_nutrition[key] = total_nutrition.get(key, 0) + value
        
        # Calculate overall meal CVD impact
        overall_cvd_impact = self._calculate_meal_cvd_impact(meal_analyses)
        
        return {
            'success': True,
            'meal_count': len(meal_analyses),
            'individual_analyses': meal_analyses,
            'total_nutrition': total_nutrition,
            'overall_cvd_impact': overall_cvd_impact,
            'meal_recommendations': self._generate_meal_recommendations(meal_analyses),
            'analysis_timestamp': current_timestamp()
        }
    
    def _validate_image(self, image_path: str) -> Dict[str, Any]:
        """Validate image file for processing"""
        if not os.path.exists(image_path):
            return {'valid': False, 'error': f"Image not found: {image_path}"}
        
        # Check file extension
        file_ext = os.path.splitext(image_path)[1].lower()
        if file_ext not in self.supported_formats:
            return {'valid': False, 'error': f"Unsupported format: {file_ext}"}
        
        # Check file size
        file_size = os.path.getsize(image_path)
        if file_size > self.max_image_size:
            return {'valid': False, 'error': f"Image too large: {file_size} bytes"}
        
        if file_size == 0:
            return {'valid': False, 'error': "Empty file"}
        
        return {'valid': True}
    
    def _prepare_image(self, image_path: str) -> str:
        """Prepare image for API (resize if needed)"""
        try:
            with Image.open(image_path) as img:
                # Check if image needs resizing
                max_dimension = 2048
                if max(img.size) > max_dimension:
                    # Resize while maintaining aspect ratio
                    img.thumbnail((max_dimension, max_dimension), Image.Resampling.LANCZOS)
                    
                    # Save to temporary file
                    temp_path = image_path.replace('.', '_resized.')
                    img.save(temp_path, optimize=True, quality=85)
                    return temp_path
                
                return image_path
                
        except Exception as e:
            print(f"Warning: Could not process image {image_path}: {e}")
            return image_path
    
    def _analyze_with_vision_api(self, image_path: str, user_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Analyze image using OpenAI Vision API"""
        try:
            # Encode image to base64
            with open(image_path, "rb") as image_file:
                base64_image = base64.b64encode(image_file.read()).decode('utf-8')
            
            # Build prompt with user context
            prompt = self.nutrition_prompt
            if user_context:
                prompt += f"\n\nUser context: Age {user_context.get('age', 'unknown')}"
                if 'profile' in user_context:
                    lifestyle = user_context['profile'].get('lifestyle', {})
                    prompt += f", Exercise: {lifestyle.get('exercise_frequency', 0)} times/week"
            
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {
                            "type": "image_url",
                            "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}
                        }
                    ]
                }
            ]
            
            if OPENAI_CLIENT_AVAILABLE:
                response = client.chat.completions.create(
                    model="gpt-4-vision-preview",
                    messages=messages,
                    max_tokens=600,
                    temperature=0.3
                )
            else:
                response = openai.ChatCompletion.create(
                    model="gpt-4-vision-preview",
                    messages=messages,
                    max_tokens=600,
                    temperature=0.3
                )
            
            return {
                'success': True,
                'raw_response': response.choices[0].message.content
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"Vision API error: {str(e)}"
            }
    
    def _extract_nutrition_data(self, raw_response: str) -> Dict[str, Any]:
        """Extract structured nutrition data from AI response"""
        try:
            # Try to parse JSON if present
            if '{' in raw_response and '}' in raw_response:
                json_start = raw_response.find('{')
                json_end = raw_response.rfind('}') + 1
                json_str = raw_response[json_start:json_end]
                return json.loads(json_str)
        except:
            pass
        
        # Fallback: extract data using text parsing
        return self._parse_nutrition_text(raw_response)
    
    def _parse_nutrition_text(self, text: str) -> Dict[str, Any]:
        """Parse nutrition information from text response"""
        # This is a simplified parser - could be enhanced with NLP
        nutrition_data = {
            'food_name': 'Unknown food',
            'nutrition': {},
            'heart_health_score': 5,
            'cvd_risk_factors': [],
            'recommendations': []
        }
        
        lines = text.split('\n')
        for line in lines:
            line = line.strip().lower()
            
            # Extract food name
            if 'food:' in line or 'identified:' in line:
                nutrition_data['food_name'] = line.split(':')[1].strip()
            
            # Extract heart health score
            if 'heart health score' in line or 'score:' in line:
                import re
                score_match = re.search(r'(\d+)', line)
                if score_match:
                    nutrition_data['heart_health_score'] = int(score_match.group(1))
        
        return nutrition_data
    
    def _calculate_cvd_impact(self, nutrition_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate cardiovascular impact based on nutrition data"""
        nutrition = nutrition_data.get('nutrition', {})
        
        # Simple CVD risk calculation
        risk_score = 0
        risk_factors = []
        
        # High sodium
        sodium = nutrition.get('sodium', 0)
        if sodium > 600:  # mg per serving
            risk_score += 2
            risk_factors.append('High sodium')
        
        # Saturated fat
        sat_fat = nutrition.get('saturated_fat', 0)
        if sat_fat > 5:  # g per serving
            risk_score += 2
            risk_factors.append('High saturated fat')
        
        # Trans fat
        trans_fat = nutrition.get('trans_fat', 0)
        if trans_fat > 0:
            risk_score += 3
            risk_factors.append('Contains trans fat')
        
        # Low fiber (protective factor)
        fiber = nutrition.get('fiber', 0)
        if fiber < 3:
            risk_score += 1
            risk_factors.append('Low fiber')
        
        return {
            'risk_score': min(10, risk_score),
            'risk_level': 'Low' if risk_score < 3 else 'Moderate' if risk_score < 6 else 'High',
            'risk_factors': risk_factors
        }
    
    def _calculate_meal_cvd_impact(self, meal_analyses: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate overall CVD impact for a meal"""
        if not meal_analyses:
            return {'risk_score': 0, 'risk_level': 'Unknown'}
        
        total_risk = sum(analysis.get('cvd_impact', {}).get('risk_score', 0) for analysis in meal_analyses)
        avg_risk = total_risk / len(meal_analyses)
        
        return {
            'risk_score': avg_risk,
            'risk_level': 'Low' if avg_risk < 3 else 'Moderate' if avg_risk < 6 else 'High',
            'meal_count': len(meal_analyses)
        }
    
    def _generate_meal_recommendations(self, meal_analyses: List[Dict[str, Any]]) -> List[str]:
        """Generate meal-level recommendations"""
        recommendations = []
        
        # Analyze overall meal composition
        high_sodium_count = sum(1 for analysis in meal_analyses 
                               if 'High sodium' in analysis.get('cvd_impact', {}).get('risk_factors', []))
        
        if high_sodium_count > 0:
            recommendations.append("Consider reducing sodium by choosing fresh ingredients over processed foods")
        
        low_fiber_count = sum(1 for analysis in meal_analyses 
                             if 'Low fiber' in analysis.get('cvd_impact', {}).get('risk_factors', []))
        
        if low_fiber_count > len(meal_analyses) / 2:
            recommendations.append("Add more vegetables, fruits, or whole grains to increase fiber content")
        
        return recommendations
    
    def _create_error_response(self, error_message: str) -> Dict[str, Any]:
        """Create standardized error response"""
        return {
            'success': False,
            'error': error_message,
            'food_name': 'Unknown',
            'nutrition': {},
            'timestamp': current_timestamp()
        }

# Convenience functions
def analyze_food(image_path: str, user_context: Dict[str, Any] = None) -> Dict[str, Any]:
    """Main food analysis function - wrapper for the class method"""
    analyzer = FoodVisionAnalyzer()
    return analyzer.analyze_food(image_path, user_context)

def quick_food_scan(image_path: str) -> str:
    """Quick food identification that returns just the food name"""
    result = analyze_food(image_path)
    return result.get('food_name', 'Unknown food') if result.get('success') else 'Analysis failed'

# Mock function for demo purposes
def mock_analyze_food(image_path: str) -> Dict[str, Any]:
    """Mock food analysis for demo purposes"""
    return {
        'success': True,
        'food_name': 'Grilled salmon with vegetables',
        'nutrition': {
            'calories': 350,
            'saturated_fat': 2.5,
            'trans_fat': 0,
            'sodium': 180,
            'fiber': 4,
            'sugar': 6,
            'protein': 28
        },
        'heart_health_score': 9,
        'cvd_risk_factors': [],
        'recommendations': ['Excellent choice! Rich in omega-3 fatty acids and fiber.'],
        'cvd_impact': {
            'risk_score': 1,
            'risk_level': 'Low',
            'risk_factors': []
        },
        'image_path': image_path,
        'analysis_timestamp': current_timestamp(),
        'mock': True
    }

"""
Comprehensive Test Suite for MIT CVD App Implementation
Tests all real implementations and API contracts
"""
import os
import json
import tempfile
from fastapi.testclient import TestClient
import logging

# Import all modules
import crud_module
import chat_module
import audio_module
import vision_module
import cvd_score_module
import heuristic_module
import gamification_module
import pdf_module
import file_manager
import api_server

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CVDAppTester:
    """Comprehensive test suite for CVD App"""
    
    def __init__(self):
        self.test_results = {}
        self.api_client = TestClient(api_server.app)
        self.test_user_id = None
    
    def run_all_tests(self) -> dict:
        """Run all tests and return results"""
        print("🧪 Starting Comprehensive CVD App Tests")
        print("=" * 50)
        
        # Test each component
        self.test_database_functionality()
        self.test_chat_functionality()
        self.test_audio_processing()
        self.test_computer_vision()
        self.test_cvd_assessment()
        self.test_gamification()
        self.test_file_management()
        self.test_api_endpoints()
        self.test_end_to_end_workflow()
        
        # Generate summary
        self.generate_test_summary()
        
        return self.test_results
    
    def test_database_functionality(self):
        """Test enhanced database operations"""
        print("\n1️⃣ Testing Database Functionality")
        print("-" * 30)
        
        try:
            # Test user creation with validation
            user = crud_module.create_user("Test User", 35, "<EMAIL>")
            assert user is not None, "User creation failed"
            self.test_user_id = user['id']
            print("✅ User creation with validation")
            
            # Test user retrieval
            retrieved_user = crud_module.read_user(self.test_user_id)
            assert retrieved_user is not None, "User retrieval failed"
            print("✅ User retrieval")
            
            # Test user update with validation
            update_success = crud_module.update_user(self.test_user_id, {
                'profile': {'lifestyle': {'exercise_frequency': 5}}
            })
            assert update_success, "User update failed"
            print("✅ User update with validation")
            
            # Test database health check
            health = crud_module.get_database_health()
            assert health['status'] in ['healthy', 'warning'], "Database health check failed"
            print("✅ Database health monitoring")
            
            # Test backup functionality
            backup_success = crud_module.backup_database()
            assert backup_success, "Database backup failed"
            print("✅ Database backup")
            
            self.test_results['database'] = {'status': 'PASS', 'details': 'All database tests passed'}
            
        except Exception as e:
            print(f"❌ Database test failed: {e}")
            self.test_results['database'] = {'status': 'FAIL', 'error': str(e)}
    
    def test_chat_functionality(self):
        """Test AI chat functionality"""
        print("\n2️⃣ Testing Chat Functionality")
        print("-" * 30)
        
        try:
            # Test basic chat
            response = chat_module.get_insights("I have chest pain", None)
            assert response['success'], "Basic chat failed"
            print("✅ Basic AI chat")
            
            # Test chat with user context
            if self.test_user_id:
                user_context = crud_module.read_user(self.test_user_id)
                response = chat_module.get_insights("I'm feeling tired", user_context)
                assert response['success'], "Contextual chat failed"
                print("✅ Contextual AI chat")
            
            # Test audio transcript processing
            audio_input = {'type': 'audio_transcript', 'content': 'I have shortness of breath'}
            response = chat_module.get_insights(audio_input, None)
            assert response['success'], "Audio transcript chat failed"
            print("✅ Audio transcript processing")
            
            self.test_results['chat'] = {'status': 'PASS', 'details': 'All chat tests passed'}
            
        except Exception as e:
            print(f"❌ Chat test failed: {e}")
            self.test_results['chat'] = {'status': 'FAIL', 'error': str(e)}
    
    def test_audio_processing(self):
        """Test real audio processing functionality"""
        print("\n3️⃣ Testing Audio Processing")
        print("-" * 30)
        
        try:
            # Test file upload simulation
            test_audio_data = b'fake audio data for testing'
            temp_path = audio_module.save_uploaded_audio(test_audio_data, 'test.mp3')
            assert os.path.exists(temp_path), "Audio file save failed"
            print("✅ Audio file upload and save")
            
            # Test transcription methods availability
            methods = audio_module.get_available_transcription_methods()
            assert isinstance(methods, dict), "Transcription methods check failed"
            print(f"✅ Transcription methods: {list(methods.keys())}")
            
            # Test file cleanup
            cleanup_success = audio_module.cleanup_temp_audio_file(temp_path)
            assert cleanup_success, "Audio file cleanup failed"
            print("✅ Audio file cleanup")
            
            self.test_results['audio'] = {'status': 'PASS', 'details': 'All audio tests passed'}
            
        except Exception as e:
            print(f"❌ Audio test failed: {e}")
            self.test_results['audio'] = {'status': 'FAIL', 'error': str(e)}
    
    def test_computer_vision(self):
        """Test real computer vision functionality"""
        print("\n4️⃣ Testing Computer Vision")
        print("-" * 30)
        
        try:
            # Test image upload simulation
            test_image_data = b'fake image data for testing'
            temp_path = vision_module.save_uploaded_image(test_image_data, 'test.jpg')
            assert os.path.exists(temp_path), "Image file save failed"
            print("✅ Image file upload and save")
            
            # Test sample image creation
            sample_path = vision_module.create_sample_food_image()
            assert os.path.exists(sample_path), "Sample image creation failed"
            print("✅ Sample image creation")
            
            # Test file cleanup
            cleanup_success = vision_module.cleanup_temp_image_file(temp_path)
            assert cleanup_success, "Image file cleanup failed"
            print("✅ Image file cleanup")
            
            self.test_results['vision'] = {'status': 'PASS', 'details': 'All vision tests passed'}
            
        except Exception as e:
            print(f"❌ Vision test failed: {e}")
            self.test_results['vision'] = {'status': 'FAIL', 'error': str(e)}
    
    def test_cvd_assessment(self):
        """Test CVD risk assessment"""
        print("\n5️⃣ Testing CVD Assessment")
        print("-" * 30)
        
        try:
            if not self.test_user_id:
                print("⚠️ Skipping CVD test - no test user")
                return
            
            user_data = crud_module.read_user(self.test_user_id)
            
            # Test CVD risk calculation
            cvd_result = cvd_score_module.calculate_cvd_risk(user_data)
            assert cvd_result['success'], "CVD calculation failed"
            print("✅ CVD risk calculation")
            
            # Test heuristic analysis
            heuristic_result = heuristic_module.calculate_heuristic(user_data)
            assert heuristic_result['success'], "Heuristic calculation failed"
            print("✅ Heuristic analysis")
            
            self.test_results['cvd'] = {'status': 'PASS', 'details': 'All CVD tests passed'}
            
        except Exception as e:
            print(f"❌ CVD test failed: {e}")
            self.test_results['cvd'] = {'status': 'FAIL', 'error': str(e)}
    
    def test_gamification(self):
        """Test gamification functionality"""
        print("\n6️⃣ Testing Gamification")
        print("-" * 30)
        
        try:
            if not self.test_user_id:
                print("⚠️ Skipping gamification test - no test user")
                return
            
            # Test progress retrieval
            progress = gamification_module.get_user_progress(self.test_user_id)
            assert progress['success'], "Progress retrieval failed"
            print("✅ Progress retrieval")
            
            # Test activity logging
            activity_result = gamification_module.log_activity(self.test_user_id, 'test_activity')
            assert activity_result['success'], "Activity logging failed"
            print("✅ Activity logging")
            
            self.test_results['gamification'] = {'status': 'PASS', 'details': 'All gamification tests passed'}
            
        except Exception as e:
            print(f"❌ Gamification test failed: {e}")
            self.test_results['gamification'] = {'status': 'FAIL', 'error': str(e)}
    
    def test_file_management(self):
        """Test file management functionality"""
        print("\n7️⃣ Testing File Management")
        print("-" * 30)
        
        try:
            # Test file validation
            test_data = b'test file content'
            validation = file_manager.file_manager.validate_file(test_data, 'test.mp3', 'audio')
            assert validation['valid'], "File validation failed"
            print("✅ File validation")
            
            # Test file saving and cleanup
            temp_path = file_manager.save_uploaded_audio(test_data, 'test.mp3')
            assert os.path.exists(temp_path), "File save failed"
            
            cleanup_success = file_manager.cleanup_temp_file(temp_path)
            assert cleanup_success, "File cleanup failed"
            print("✅ File save and cleanup")
            
            # Test directory stats
            stats = file_manager.get_file_manager_stats()
            assert 'total_files' in stats, "Directory stats failed"
            print("✅ Directory statistics")
            
            self.test_results['file_management'] = {'status': 'PASS', 'details': 'All file management tests passed'}
            
        except Exception as e:
            print(f"❌ File management test failed: {e}")
            self.test_results['file_management'] = {'status': 'FAIL', 'error': str(e)}
    
    def test_api_endpoints(self):
        """Test REST API endpoints"""
        print("\n8️⃣ Testing API Endpoints")
        print("-" * 30)
        
        try:
            # Test health endpoint
            response = self.api_client.get("/health")
            assert response.status_code == 200, "Health endpoint failed"
            print("✅ Health endpoint")
            
            # Test users endpoint
            response = self.api_client.get("/users")
            assert response.status_code == 200, "Users endpoint failed"
            print("✅ Users endpoint")
            
            # Test chat endpoint
            response = self.api_client.post("/chat", json={"message": "test message"})
            assert response.status_code == 200, "Chat endpoint failed"
            print("✅ Chat endpoint")
            
            # Test system info endpoint
            response = self.api_client.get("/system/info")
            assert response.status_code == 200, "System info endpoint failed"
            print("✅ System info endpoint")
            
            self.test_results['api'] = {'status': 'PASS', 'details': 'All API tests passed'}
            
        except Exception as e:
            print(f"❌ API test failed: {e}")
            self.test_results['api'] = {'status': 'FAIL', 'error': str(e)}
    
    def test_end_to_end_workflow(self):
        """Test complete end-to-end workflow"""
        print("\n9️⃣ Testing End-to-End Workflow")
        print("-" * 30)
        
        try:
            if not self.test_user_id:
                print("⚠️ Skipping E2E test - no test user")
                return
            
            # Simulate complete user journey
            user_data = crud_module.read_user(self.test_user_id)
            
            # 1. Chat interaction
            chat_response = chat_module.get_insights("I'm concerned about my heart health", user_data)
            assert chat_response['success'], "E2E chat failed"
            
            # 2. CVD assessment
            cvd_result = cvd_score_module.calculate_cvd_risk(user_data)
            assert cvd_result['success'], "E2E CVD assessment failed"
            
            # 3. Activity logging
            activity_result = gamification_module.log_activity(self.test_user_id, 'health_check')
            assert activity_result['success'], "E2E activity logging failed"
            
            print("✅ Complete end-to-end workflow")
            
            self.test_results['end_to_end'] = {'status': 'PASS', 'details': 'End-to-end workflow successful'}
            
        except Exception as e:
            print(f"❌ End-to-end test failed: {e}")
            self.test_results['end_to_end'] = {'status': 'FAIL', 'error': str(e)}
    
    def generate_test_summary(self):
        """Generate test summary"""
        print("\n📊 Test Summary")
        print("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result['status'] == 'PASS')
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        print("\nDetailed Results:")
        for test_name, result in self.test_results.items():
            status_icon = "✅" if result['status'] == 'PASS' else "❌"
            print(f"{status_icon} {test_name}: {result['status']}")
            if result['status'] == 'FAIL':
                print(f"   Error: {result.get('error', 'Unknown error')}")

def main():
    """Run the comprehensive test suite"""
    tester = CVDAppTester()
    results = tester.run_all_tests()
    
    # Save results to file
    with open('test_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n📁 Test results saved to: test_results.json")
    
    return results

if __name__ == "__main__":
    main()

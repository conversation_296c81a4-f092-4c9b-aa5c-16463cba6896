"""
Local Whisper Installation Script for MIT CVD App
Installs OpenAI Whisper for offline audio transcription
"""
import subprocess
import sys
import os

def install_whisper():
    """Install OpenAI Whisper package"""
    print("🎤 Installing OpenAI Whisper for local transcription...")
    print("This enables offline audio transcription without API costs.")
    
    try:
        # Install openai-whisper
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "openai-whisper"
        ])
        
        print("✅ OpenAI Whisper installed successfully!")
        
        # Test the installation
        try:
            import whisper
            print("✅ Whisper import test successful")
            
            # Show available models
            models = ["tiny", "base", "small", "medium", "large", "turbo"]
            print(f"\n📋 Available Whisper models: {', '.join(models)}")
            print("💡 Recommended: 'turbo' for best speed/accuracy balance")
            
            # Download a small model for testing
            print("\n⬇️ Downloading 'tiny' model for testing...")
            model = whisper.load_model("tiny")
            print("✅ Test model downloaded successfully!")
            
            return True
            
        except Exception as e:
            print(f"⚠️ Installation successful but import failed: {e}")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install Whisper: {e}")
        print("\n💡 Try manual installation:")
        print("   pip install openai-whisper")
        return False

def check_system_requirements():
    """Check if system meets requirements for Whisper"""
    print("🔍 Checking system requirements...")
    
    # Check Python version
    python_version = sys.version_info
    if python_version < (3, 8):
        print(f"❌ Python 3.8+ required (current: {python_version.major}.{python_version.minor})")
        return False
    else:
        print(f"✅ Python version: {python_version.major}.{python_version.minor}")
    
    # Check available disk space (rough estimate)
    try:
        import shutil
        free_space = shutil.disk_usage('.').free / (1024**3)  # GB
        if free_space < 1:
            print(f"⚠️ Low disk space: {free_space:.1f}GB (models require ~1GB)")
        else:
            print(f"✅ Disk space: {free_space:.1f}GB available")
    except:
        print("⚠️ Could not check disk space")
    
    return True

def show_usage_examples():
    """Show usage examples for local Whisper"""
    print("\n📖 Usage Examples:")
    print("=" * 40)
    
    print("\n1. Basic transcription:")
    print("   import audio_module")
    print("   result = audio_module.transcribe_audio('audio.wav')")
    print("   print(result['transcript'])")
    
    print("\n2. Force local Whisper:")
    print("   result = audio_module.transcribe_with_local_whisper('audio.wav', model='turbo')")
    
    print("\n3. Check available methods:")
    print("   methods = audio_module.get_available_transcription_methods()")
    print("   print(methods)")
    
    print("\n4. In the main app:")
    print("   python3 main.py")
    print("   # Select option 4 (Audio Input) to test transcription")

def main():
    """Main installation function"""
    print("🏥 MIT CVD App - Local Whisper Installation")
    print("=" * 50)
    
    # Check if already installed
    try:
        import whisper
        print("✅ OpenAI Whisper is already installed!")
        
        # Show available models
        models = ["tiny", "base", "small", "medium", "large", "turbo"]
        print(f"📋 Available models: {', '.join(models)}")
        
        show_usage_examples()
        return
        
    except ImportError:
        print("📦 OpenAI Whisper not found. Installing...")
    
    # Check system requirements
    if not check_system_requirements():
        print("❌ System requirements not met")
        return
    
    # Ask for confirmation
    response = input("\nInstall OpenAI Whisper? (y/n): ").lower().strip()
    if not response.startswith('y'):
        print("Installation cancelled")
        return
    
    # Install Whisper
    if install_whisper():
        print("\n🎉 Installation completed successfully!")
        show_usage_examples()
        
        print("\n🚀 Next steps:")
        print("1. Run: python3 main.py")
        print("2. Select option 4 (Audio Input)")
        print("3. Choose option 2 to test local Whisper")
        
    else:
        print("\n❌ Installation failed")
        print("Please try manual installation or check the error messages above")

if __name__ == "__main__":
    main()

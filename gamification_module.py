"""
Gamification & Streak Tracking Module for MIT CVD App
Handles user engagement, streaks, achievements, and point systems
"""
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import json

from config import STREAK_BONUS_MULTIPLIER, MAX_DAILY_POINTS
from data_models import current_timestamp
from crud_module import read_streak, update_streak, create_streak

class GamificationEngine:
    """Gamification system for CVD health tracking"""
    
    def __init__(self):
        # Achievement definitions
        self.achievements = {
            'first_checkin': {
                'name': 'Health Journey Begins',
                'description': 'Complete your first health check-in',
                'points': 50,
                'icon': '🎯'
            },
            'week_streak': {
                'name': 'Week Warrior',
                'description': 'Maintain a 7-day check-in streak',
                'points': 100,
                'icon': '🔥'
            },
            'month_streak': {
                'name': 'Monthly Master',
                'description': 'Maintain a 30-day check-in streak',
                'points': 500,
                'icon': '👑'
            },
            'nutrition_logger': {
                'name': 'Nutrition Tracker',
                'description': 'Log 10 meals',
                'points': 75,
                'icon': '🥗'
            },
            'exercise_enthusiast': {
                'name': 'Exercise Enthusiast',
                'description': 'Report exercise 5 times',
                'points': 100,
                'icon': '💪'
            },
            'health_improver': {
                'name': 'Health Improver',
                'description': 'Show improvement in CVD risk score',
                'points': 200,
                'icon': '📈'
            },
            'consistent_user': {
                'name': 'Consistency Champion',
                'description': 'Use the app for 14 consecutive days',
                'points': 300,
                'icon': '⭐'
            }
        }
        
        # Point values for different activities
        self.activity_points = {
            'daily_checkin': 10,
            'nutrition_log': 5,
            'exercise_report': 8,
            'health_chat': 3,
            'cvd_assessment': 15,
            'goal_completion': 20
        }
    
    def update_streak(self, user_id: str, activity_type: str = 'daily_checkin') -> Dict[str, Any]:
        """
        Update user's streak and calculate points
        
        Args:
            user_id: User identifier
            activity_type: Type of activity performed
            
        Returns:
            Dict with updated streak info, points earned, and achievements
        """
        try:
            # Get current streak data
            streak_data = read_streak(user_id)
            if not streak_data:
                streak_data = create_streak(user_id)
            
            today = datetime.now().date()
            last_checkin_str = streak_data.get('last_checkin')
            
            # Parse last check-in date
            if last_checkin_str:
                try:
                    last_checkin = datetime.fromisoformat(last_checkin_str).date()
                except:
                    last_checkin = None
            else:
                last_checkin = None
            
            # Calculate streak update
            streak_update = self._calculate_streak_update(today, last_checkin, streak_data)
            
            # Calculate points for this activity
            base_points = self.activity_points.get(activity_type, 5)
            streak_multiplier = min(2.0, 1.0 + (streak_update['current_streak'] * 0.1))
            points_earned = int(base_points * streak_multiplier)
            points_earned = min(points_earned, MAX_DAILY_POINTS)
            
            # Update streak data
            updated_data = {
                'current_streak': streak_update['current_streak'],
                'longest_streak': max(streak_data.get('longest_streak', 0), streak_update['current_streak']),
                'total_checkins': streak_data.get('total_checkins', 0) + 1,
                'last_checkin': today.isoformat(),
                'points': streak_data.get('points', 0) + points_earned
            }
            
            # Check for new achievements
            new_achievements = self._check_achievements(user_id, updated_data, activity_type)
            
            # Add achievement points
            achievement_points = sum(self.achievements[ach]['points'] for ach in new_achievements)
            updated_data['points'] += achievement_points
            
            # Update achievements list
            current_achievements = streak_data.get('achievements', [])
            updated_data['achievements'] = list(set(current_achievements + new_achievements))
            
            # Save updated data
            update_streak(user_id, updated_data)
            
            return {
                'success': True,
                'streak_info': {
                    'current_streak': updated_data['current_streak'],
                    'longest_streak': updated_data['longest_streak'],
                    'total_checkins': updated_data['total_checkins'],
                    'total_points': updated_data['points']
                },
                'points_earned': points_earned + achievement_points,
                'base_points': points_earned,
                'achievement_points': achievement_points,
                'new_achievements': [
                    {
                        'id': ach_id,
                        'name': self.achievements[ach_id]['name'],
                        'description': self.achievements[ach_id]['description'],
                        'points': self.achievements[ach_id]['points'],
                        'icon': self.achievements[ach_id]['icon']
                    }
                    for ach_id in new_achievements
                ],
                'streak_broken': streak_update.get('streak_broken', False),
                'streak_multiplier': streak_multiplier,
                'timestamp': current_timestamp()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"Streak update error: {str(e)}",
                'points_earned': 0
            }
    
    def get_user_progress(self, user_id: str) -> Dict[str, Any]:
        """Get comprehensive user progress and gamification status"""
        try:
            streak_data = read_streak(user_id)
            if not streak_data:
                return {
                    'success': False,
                    'error': 'No streak data found for user'
                }
            
            # Calculate progress metrics
            progress = self._calculate_progress_metrics(streak_data)
            
            # Get available achievements
            available_achievements = self._get_available_achievements(streak_data)
            
            # Calculate level and next level requirements
            level_info = self._calculate_level(streak_data.get('points', 0))
            
            return {
                'success': True,
                'current_streak': streak_data.get('current_streak', 0),
                'longest_streak': streak_data.get('longest_streak', 0),
                'total_checkins': streak_data.get('total_checkins', 0),
                'total_points': streak_data.get('points', 0),
                'level': level_info['level'],
                'level_progress': level_info['progress'],
                'points_to_next_level': level_info['points_to_next'],
                'achievements_earned': len(streak_data.get('achievements', [])),
                'achievements_available': len(available_achievements),
                'earned_achievements': [
                    {
                        'id': ach_id,
                        'name': self.achievements[ach_id]['name'],
                        'description': self.achievements[ach_id]['description'],
                        'icon': self.achievements[ach_id]['icon']
                    }
                    for ach_id in streak_data.get('achievements', [])
                    if ach_id in self.achievements
                ],
                'available_achievements': available_achievements,
                'progress_metrics': progress,
                'last_checkin': streak_data.get('last_checkin'),
                'timestamp': current_timestamp()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"Progress retrieval error: {str(e)}"
            }
    
    def _calculate_streak_update(self, today: datetime.date, last_checkin: Optional[datetime.date], 
                                streak_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate how the streak should be updated"""
        current_streak = streak_data.get('current_streak', 0)
        
        if not last_checkin:
            # First check-in ever
            return {
                'current_streak': 1,
                'streak_broken': False,
                'first_checkin': True
            }
        
        days_since_last = (today - last_checkin).days
        
        if days_since_last == 0:
            # Same day check-in (no streak change)
            return {
                'current_streak': current_streak,
                'streak_broken': False,
                'same_day': True
            }
        elif days_since_last == 1:
            # Consecutive day (streak continues)
            return {
                'current_streak': current_streak + 1,
                'streak_broken': False
            }
        else:
            # Streak broken (more than 1 day gap)
            return {
                'current_streak': 1,
                'streak_broken': True,
                'days_missed': days_since_last - 1
            }
    
    def _check_achievements(self, user_id: str, streak_data: Dict[str, Any], activity_type: str) -> List[str]:
        """Check for newly earned achievements"""
        current_achievements = set(streak_data.get('achievements', []))
        new_achievements = []
        
        # First check-in achievement
        if 'first_checkin' not in current_achievements and streak_data.get('total_checkins', 0) >= 1:
            new_achievements.append('first_checkin')
        
        # Streak achievements
        current_streak = streak_data.get('current_streak', 0)
        if 'week_streak' not in current_achievements and current_streak >= 7:
            new_achievements.append('week_streak')
        
        if 'month_streak' not in current_achievements and current_streak >= 30:
            new_achievements.append('month_streak')
        
        if 'consistent_user' not in current_achievements and current_streak >= 14:
            new_achievements.append('consistent_user')
        
        # Activity-specific achievements (would need additional data from user profile)
        # These would require integration with other modules to check nutrition logs, exercise reports, etc.
        
        return new_achievements
    
    def _calculate_progress_metrics(self, streak_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate various progress metrics"""
        total_checkins = streak_data.get('total_checkins', 0)
        current_streak = streak_data.get('current_streak', 0)
        longest_streak = streak_data.get('longest_streak', 0)
        
        # Calculate consistency percentage (last 30 days)
        last_checkin_str = streak_data.get('last_checkin')
        if last_checkin_str:
            try:
                last_checkin = datetime.fromisoformat(last_checkin_str).date()
                days_since_start = min(30, (datetime.now().date() - last_checkin).days + current_streak)
                consistency = (current_streak / days_since_start) * 100 if days_since_start > 0 else 0
            except:
                consistency = 0
        else:
            consistency = 0
        
        return {
            'consistency_percentage': round(consistency, 1),
            'streak_health': 'Excellent' if current_streak >= 14 else 'Good' if current_streak >= 7 else 'Building',
            'engagement_level': 'High' if total_checkins >= 30 else 'Medium' if total_checkins >= 10 else 'New',
            'streak_ratio': round((current_streak / max(longest_streak, 1)) * 100, 1)
        }
    
    def _get_available_achievements(self, streak_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get achievements that are still available to earn"""
        earned_achievements = set(streak_data.get('achievements', []))
        available = []
        
        for ach_id, ach_data in self.achievements.items():
            if ach_id not in earned_achievements:
                # Add progress information for each achievement
                progress = self._get_achievement_progress(ach_id, streak_data)
                available.append({
                    'id': ach_id,
                    'name': ach_data['name'],
                    'description': ach_data['description'],
                    'points': ach_data['points'],
                    'icon': ach_data['icon'],
                    'progress': progress
                })
        
        return available
    
    def _get_achievement_progress(self, achievement_id: str, streak_data: Dict[str, Any]) -> Dict[str, Any]:
        """Get progress towards a specific achievement"""
        current_streak = streak_data.get('current_streak', 0)
        total_checkins = streak_data.get('total_checkins', 0)
        
        if achievement_id == 'first_checkin':
            return {'current': min(1, total_checkins), 'required': 1, 'percentage': min(100, total_checkins * 100)}
        elif achievement_id == 'week_streak':
            return {'current': min(7, current_streak), 'required': 7, 'percentage': min(100, (current_streak / 7) * 100)}
        elif achievement_id == 'month_streak':
            return {'current': min(30, current_streak), 'required': 30, 'percentage': min(100, (current_streak / 30) * 100)}
        elif achievement_id == 'consistent_user':
            return {'current': min(14, current_streak), 'required': 14, 'percentage': min(100, (current_streak / 14) * 100)}
        else:
            return {'current': 0, 'required': 1, 'percentage': 0}
    
    def _calculate_level(self, total_points: int) -> Dict[str, Any]:
        """Calculate user level based on total points"""
        # Level thresholds
        level_thresholds = [0, 100, 300, 600, 1000, 1500, 2200, 3000, 4000, 5500, 7500]
        
        level = 1
        for threshold in level_thresholds:
            if total_points >= threshold:
                level = level_thresholds.index(threshold) + 1
            else:
                break
        
        # Calculate progress to next level
        if level <= len(level_thresholds):
            current_threshold = level_thresholds[level - 1] if level > 1 else 0
            next_threshold = level_thresholds[level] if level < len(level_thresholds) else level_thresholds[-1] + 2000
            
            points_in_level = total_points - current_threshold
            points_needed = next_threshold - current_threshold
            progress_percentage = (points_in_level / points_needed) * 100 if points_needed > 0 else 100
            
            return {
                'level': level,
                'progress': round(progress_percentage, 1),
                'points_to_next': next_threshold - total_points,
                'current_level_points': points_in_level,
                'next_level_requirement': points_needed
            }
        else:
            return {
                'level': level,
                'progress': 100,
                'points_to_next': 0,
                'current_level_points': total_points,
                'next_level_requirement': 0
            }

# Convenience functions
def update_streak(user_id: str, activity_type: str = 'daily_checkin') -> Dict[str, Any]:
    """Main streak update function - wrapper for the class method"""
    engine = GamificationEngine()
    return engine.update_streak(user_id, activity_type)

def get_user_progress(user_id: str) -> Dict[str, Any]:
    """Get user gamification progress - wrapper for the class method"""
    engine = GamificationEngine()
    return engine.get_user_progress(user_id)

def quick_checkin(user_id: str) -> Dict[str, Any]:
    """Quick daily check-in function"""
    return update_streak(user_id, 'daily_checkin')

def log_activity(user_id: str, activity_type: str) -> Dict[str, Any]:
    """Log any type of activity for points and achievements"""
    return update_streak(user_id, activity_type)

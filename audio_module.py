"""
Audio Transcription Module for MIT CVD App
Handles audio-to-text conversion using OpenAI Whisper API
"""
import openai
import os
from typing import Dict, Any, Optional
import tempfile

# Optional import for audio processing
try:
    from pydub import AudioSegment
    PYDUB_AVAILABLE = True
except ImportError:
    PYDUB_AVAILABLE = False
    print("Warning: pydub not available. Audio processing will use mock functions.")

# Optional import for local Whisper
try:
    import whisper
    LOCAL_WHISPER_AVAILABLE = True
    print("Local Whisper model available for offline transcription")
except ImportError:
    LOCAL_WHISPER_AVAILABLE = False
    print("Local Whisper not available. Will use OpenAI API or mock functions.")

from config import OPENAI_API_KEY
from data_models import current_timestamp

# Initialize OpenAI client
if OPENAI_API_KEY:
    try:
        from openai import OpenAI
        client = OpenAI(api_key=OPENAI_API_KEY)
        OPENAI_CLIENT_AVAILABLE = True
    except ImportError:
        openai.api_key = OPENAI_API_KEY
        OPENAI_CLIENT_AVAILABLE = False
else:
    OPENAI_CLIENT_AVAILABLE = False

class AudioProcessor:
    """Audio processing and transcription using local Whisper or OpenAI API"""

    def __init__(self):
        self.supported_formats = ['.mp3', '.mp4', '.mpeg', '.mpga', '.m4a', '.wav', '.webm']
        self.max_file_size = 25 * 1024 * 1024  # 25MB limit for Whisper API
        self.local_whisper_model = None
        self.preferred_model = "turbo"  # Default local Whisper model
    
    def transcribe_audio(self, file_path: str, language: str = None) -> Dict[str, Any]:
        """
        Transcribe audio file to text using local Whisper or OpenAI API

        Args:
            file_path: Path to audio file
            language: Optional language code (e.g., 'en', 'es')

        Returns:
            Dict with transcription result and metadata
        """
        try:
            # Validate file
            validation_result = self._validate_audio_file(file_path)
            if not validation_result['valid']:
                return self._create_error_response(validation_result['error'])

            # Try local Whisper first (faster, no API costs, works offline)
            if LOCAL_WHISPER_AVAILABLE:
                try:
                    return self._transcribe_with_local_whisper(file_path, language)
                except Exception as e:
                    print(f"Local Whisper failed: {e}. Falling back to OpenAI API...")

            # Fallback to OpenAI API
            if OPENAI_CLIENT_AVAILABLE or OPENAI_API_KEY:
                return self._transcribe_with_openai_api(file_path, language)

            # If neither is available, return error
            return self._create_error_response(
                "No transcription method available. Install local whisper or configure OpenAI API key."
            )

        except Exception as e:
            return self._create_error_response(f"Transcription error: {str(e)}")

    def _transcribe_with_local_whisper(self, file_path: str, language: str = None) -> Dict[str, Any]:
        """
        Transcribe audio using local Whisper model

        Args:
            file_path: Path to audio file
            language: Optional language code

        Returns:
            Dict with transcription result and metadata
        """
        try:
            # Load model (cache it for subsequent calls)
            if self.local_whisper_model is None:
                print(f"Loading local Whisper model: {self.preferred_model}")
                self.local_whisper_model = whisper.load_model(self.preferred_model)

            # Load and preprocess audio
            audio = whisper.load_audio(file_path)
            audio = whisper.pad_or_trim(audio)

            # Make log-Mel spectrogram and move to the same device as the model
            mel = whisper.log_mel_spectrogram(audio, n_mels=self.local_whisper_model.dims.n_mels).to(self.local_whisper_model.device)

            # Detect the spoken language if not specified
            detected_language = 'unknown'
            language_confidence = 0.0

            if language is None:
                _, probs = self.local_whisper_model.detect_language(mel)
                detected_language = max(probs, key=probs.get)
                language_confidence = probs[detected_language]
                print(f"Detected language: {detected_language} (confidence: {language_confidence:.2f})")
            else:
                detected_language = language
                language_confidence = 1.0

            # Set up decoding options
            decode_options = whisper.DecodingOptions(
                language=detected_language if language or language_confidence > 0.5 else None,
                without_timestamps=False
            )

            # Decode the audio
            result = whisper.decode(self.local_whisper_model, mel, decode_options)

            # Calculate duration (approximate)
            duration = len(audio) / whisper.audio.SAMPLE_RATE

            # Format response to match OpenAI API structure
            return {
                'success': True,
                'transcript': result.text,
                'language': detected_language,
                'duration': duration,
                'segments': self._format_local_whisper_segments(result),
                'confidence': language_confidence,
                'original_file': file_path,
                'timestamp': current_timestamp(),
                'transcription_method': 'local_whisper',
                'model_used': self.preferred_model
            }

        except Exception as e:
            raise Exception(f"Local Whisper transcription failed: {str(e)}")

    def _transcribe_with_openai_api(self, file_path: str, language: str = None) -> Dict[str, Any]:
        """
        Transcribe audio using OpenAI Whisper API

        Args:
            file_path: Path to audio file
            language: Optional language code

        Returns:
            Dict with transcription result and metadata
        """
        try:
            # Process file if needed (convert format, compress, etc.)
            processed_file_path = self._prepare_audio_file(file_path)

            # Transcribe using Whisper API
            with open(processed_file_path, 'rb') as audio_file:
                transcript_params = {
                    'file': audio_file,
                    'model': 'whisper-1',
                    'response_format': 'verbose_json'
                }

                if language:
                    transcript_params['language'] = language

                if OPENAI_CLIENT_AVAILABLE:
                    transcript = client.audio.transcriptions.create(**transcript_params)
                else:
                    transcript = openai.Audio.transcribe(**transcript_params)

            # Clean up temporary file if created
            if processed_file_path != file_path:
                os.remove(processed_file_path)

            return {
                'success': True,
                'transcript': transcript.text,
                'language': transcript.language if hasattr(transcript, 'language') else 'unknown',
                'duration': transcript.duration if hasattr(transcript, 'duration') else None,
                'segments': transcript.segments if hasattr(transcript, 'segments') else [],
                'confidence': self._calculate_confidence(transcript),
                'original_file': file_path,
                'timestamp': current_timestamp(),
                'transcription_method': 'openai_api'
            }

        except Exception as e:
            raise Exception(f"OpenAI API transcription failed: {str(e)}")

    def _format_local_whisper_segments(self, result) -> list:
        """
        Format local Whisper result segments to match OpenAI API structure

        Args:
            result: Whisper decode result

        Returns:
            List of segment dictionaries
        """
        segments = []

        # Local Whisper doesn't provide detailed segments in the same way as API
        # Create a single segment for the entire transcript
        if hasattr(result, 'text') and result.text:
            segments.append({
                'id': 0,
                'start': 0.0,
                'end': 30.0,  # Default segment length
                'text': result.text,
                'avg_logprob': getattr(result, 'avg_logprob', -0.5),
                'no_speech_prob': getattr(result, 'no_speech_prob', 0.1)
            })

        return segments

    def transcribe_audio_for_health_chat(self, file_path: str) -> Dict[str, Any]:
        """
        Transcribe audio specifically for health conversations
        Includes health-specific processing and validation
        """
        result = self.transcribe_audio(file_path)
        
        if result['success']:
            # Add health-specific processing
            transcript = result['transcript']
            
            # Basic health keyword detection
            health_keywords = self._detect_health_keywords(transcript)
            
            result.update({
                'health_keywords': health_keywords,
                'contains_health_content': len(health_keywords) > 0,
                'processed_for_health': True
            })
        
        return result
    
    def _validate_audio_file(self, file_path: str) -> Dict[str, Any]:
        """Validate audio file for processing"""
        if not os.path.exists(file_path):
            return {'valid': False, 'error': f"File not found: {file_path}"}
        
        # Check file extension
        file_ext = os.path.splitext(file_path)[1].lower()
        if file_ext not in self.supported_formats:
            return {'valid': False, 'error': f"Unsupported format: {file_ext}"}
        
        # Check file size
        file_size = os.path.getsize(file_path)
        if file_size > self.max_file_size:
            return {'valid': False, 'error': f"File too large: {file_size} bytes (max: {self.max_file_size})"}
        
        if file_size == 0:
            return {'valid': False, 'error': "Empty file"}
        
        return {'valid': True}
    
    def _prepare_audio_file(self, file_path: str) -> str:
        """
        Prepare audio file for Whisper API
        Convert format or compress if needed
        """
        if not PYDUB_AVAILABLE:
            return file_path  # Return original file if pydub not available

        file_ext = os.path.splitext(file_path)[1].lower()
        file_size = os.path.getsize(file_path)

        # If file is already in good format and size, return as-is
        if file_ext in ['.mp3', '.wav'] and file_size < self.max_file_size * 0.8:
            return file_path

        try:
            # Load audio file
            audio = AudioSegment.from_file(file_path)

            # Create temporary file
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.mp3')
            temp_path = temp_file.name
            temp_file.close()

            # Export as MP3 with compression if needed
            if file_size > self.max_file_size * 0.8:
                # Reduce bitrate for compression
                audio.export(temp_path, format="mp3", bitrate="64k")
            else:
                # Standard quality
                audio.export(temp_path, format="mp3", bitrate="128k")

            return temp_path

        except Exception as e:
            print(f"Warning: Could not process audio file {file_path}: {e}")
            return file_path  # Return original file and hope for the best
    
    def _calculate_confidence(self, transcript) -> float:
        """Calculate overall confidence score from transcript segments"""
        if not hasattr(transcript, 'segments') or not transcript.segments:
            return 0.8  # Default confidence
        
        # Average confidence from segments (if available)
        confidences = []
        for segment in transcript.segments:
            if hasattr(segment, 'avg_logprob'):
                # Convert log probability to confidence (rough approximation)
                confidence = min(1.0, max(0.0, (segment.avg_logprob + 1.0)))
                confidences.append(confidence)
        
        return sum(confidences) / len(confidences) if confidences else 0.8
    
    def _detect_health_keywords(self, transcript: str) -> list:
        """Detect health-related keywords in transcript"""
        health_keywords = [
            'chest pain', 'shortness of breath', 'heart rate', 'blood pressure',
            'cholesterol', 'diabetes', 'exercise', 'diet', 'stress', 'sleep',
            'medication', 'symptoms', 'fatigue', 'dizziness', 'palpitations',
            'family history', 'smoking', 'alcohol', 'weight', 'nutrition'
        ]
        
        transcript_lower = transcript.lower()
        found_keywords = []
        
        for keyword in health_keywords:
            if keyword in transcript_lower:
                found_keywords.append(keyword)
        
        return found_keywords
    
    def _create_error_response(self, error_message: str) -> Dict[str, Any]:
        """Create standardized error response"""
        return {
            'success': False,
            'error': error_message,
            'transcript': '',
            'timestamp': current_timestamp()
        }

# Convenience functions
def transcribe_audio(file_path: str, language: str = None) -> Dict[str, Any]:
    """Main transcription function - wrapper for the class method"""
    processor = AudioProcessor()
    return processor.transcribe_audio(file_path, language)

def transcribe_health_audio(file_path: str) -> Dict[str, Any]:
    """Transcribe audio specifically for health conversations"""
    processor = AudioProcessor()
    return processor.transcribe_audio_for_health_chat(file_path)

def quick_voice_note(file_path: str) -> str:
    """Quick transcription that returns just the text"""
    result = transcribe_audio(file_path)
    return result.get('transcript', '') if result.get('success') else ''

def transcribe_with_local_whisper(file_path: str, model: str = "turbo") -> Dict[str, Any]:
    """
    Force transcription using local Whisper model

    Args:
        file_path: Path to audio file
        model: Whisper model to use ('tiny', 'base', 'small', 'medium', 'large', 'turbo')

    Returns:
        Dict with transcription result
    """
    if not LOCAL_WHISPER_AVAILABLE:
        return {
            'success': False,
            'error': 'Local Whisper not available. Install with: pip install openai-whisper',
            'transcript': ''
        }

    processor = AudioProcessor()
    processor.preferred_model = model
    return processor._transcribe_with_local_whisper(file_path)

def transcribe_with_openai_api(file_path: str, language: str = None) -> Dict[str, Any]:
    """
    Force transcription using OpenAI API

    Args:
        file_path: Path to audio file
        language: Optional language code

    Returns:
        Dict with transcription result
    """
    if not (OPENAI_CLIENT_AVAILABLE or OPENAI_API_KEY):
        return {
            'success': False,
            'error': 'OpenAI API not available. Configure OPENAI_API_KEY in .env file',
            'transcript': ''
        }

    processor = AudioProcessor()
    return processor._transcribe_with_openai_api(file_path, language)

def get_available_transcription_methods() -> Dict[str, bool]:
    """
    Check which transcription methods are available

    Returns:
        Dict with availability status for each method
    """
    return {
        'local_whisper': LOCAL_WHISPER_AVAILABLE,
        'openai_api': OPENAI_CLIENT_AVAILABLE or bool(OPENAI_API_KEY),
        'mock_transcription': True  # Always available for testing
    }

# Mock function for demo purposes (if OpenAI API is not available)
def mock_transcribe_audio(file_path: str) -> Dict[str, Any]:
    """Mock transcription for demo purposes"""
    return {
        'success': True,
        'transcript': "I've been feeling some chest tightness lately, especially after climbing stairs. I'm also concerned about my family history of heart disease.",
        'language': 'en',
        'duration': 15.5,
        'confidence': 0.95,
        'health_keywords': ['chest tightness', 'family history', 'heart disease'],
        'contains_health_content': True,
        'original_file': file_path,
        'timestamp': current_timestamp(),
        'mock': True
    }

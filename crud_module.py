"""
CRUD Operations Module for MIT CVD App
Handles all JSON database operations for users, sessions, and streaks
Enhanced with validation, error handling, and backup mechanisms
"""
import json
import os
import shutil
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging

from config import USERS_DB_PATH, SESSIONS_DB_PATH, STREAKS_DB_PATH, DATA_DIR
from data_models import UserModel, SessionModel, StreakModel, current_timestamp

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class JSONDatabase:
    """Enhanced JSON file database operations with validation and backup"""

    @staticmethod
    def load_data(file_path: str) -> List[Dict[str, Any]]:
        """Load data from JSON file with error recovery"""
        if not os.path.exists(file_path):
            logger.info(f"Database file {file_path} does not exist, creating empty database")
            return []

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                if not isinstance(data, list):
                    logger.warning(f"Database file {file_path} contains non-list data, resetting to empty list")
                    return []
                return data
        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error in {file_path}: {e}")
            # Try to restore from backup
            backup_path = f"{file_path}.backup"
            if os.path.exists(backup_path):
                logger.info(f"Attempting to restore from backup: {backup_path}")
                try:
                    with open(backup_path, 'r', encoding='utf-8') as f:
                        return json.load(f)
                except Exception as backup_e:
                    logger.error(f"Backup restore failed: {backup_e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error loading {file_path}: {e}")
            return []

    @staticmethod
    def save_data(file_path: str, data: List[Dict[str, Any]]) -> bool:
        """Save data to JSON file with backup and validation"""
        try:
            # Validate data structure
            if not isinstance(data, list):
                logger.error(f"Invalid data type for {file_path}: expected list, got {type(data)}")
                return False

            # Validate each item in the list
            for i, item in enumerate(data):
                if not isinstance(item, dict):
                    logger.error(f"Invalid item type at index {i} in {file_path}: expected dict, got {type(item)}")
                    return False

            # Create backup of existing file
            if os.path.exists(file_path):
                backup_path = f"{file_path}.backup"
                try:
                    shutil.copy2(file_path, backup_path)
                    logger.debug(f"Created backup: {backup_path}")
                except Exception as e:
                    logger.warning(f"Could not create backup for {file_path}: {e}")

            # Write to temporary file first
            temp_path = f"{file_path}.tmp"
            with open(temp_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

            # Atomic move to final location
            shutil.move(temp_path, file_path)
            logger.debug(f"Successfully saved data to {file_path}")
            return True

        except Exception as e:
            logger.error(f"Error saving data to {file_path}: {e}")
            # Clean up temporary file if it exists
            temp_path = f"{file_path}.tmp"
            if os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                except:
                    pass
            return False

    @staticmethod
    def validate_user_data(user_data: Dict[str, Any]) -> bool:
        """Validate user data structure"""
        required_fields = ['id', 'name', 'age', 'created_at']

        for field in required_fields:
            if field not in user_data:
                logger.error(f"Missing required field: {field}")
                return False

        # Validate data types
        if not isinstance(user_data['name'], str) or not user_data['name'].strip():
            logger.error("Invalid name: must be non-empty string")
            return False

        if not isinstance(user_data['age'], int) or user_data['age'] < 0 or user_data['age'] > 150:
            logger.error("Invalid age: must be integer between 0 and 150")
            return False

        return True

    @staticmethod
    def get_database_stats() -> Dict[str, Any]:
        """Get statistics about the database files"""
        stats = {}

        for db_name, db_path in [
            ('users', USERS_DB_PATH),
            ('sessions', SESSIONS_DB_PATH),
            ('streaks', STREAKS_DB_PATH)
        ]:
            if os.path.exists(db_path):
                file_size = os.path.getsize(db_path)
                data = JSONDatabase.load_data(db_path)
                stats[db_name] = {
                    'file_size': file_size,
                    'record_count': len(data),
                    'last_modified': datetime.fromtimestamp(os.path.getmtime(db_path)).isoformat()
                }
            else:
                stats[db_name] = {
                    'file_size': 0,
                    'record_count': 0,
                    'last_modified': None
                }

        return stats

# User CRUD Operations
def create_user(name: str, age: int, email: str = None) -> Optional[Dict[str, Any]]:
    """Create a new user with validation"""
    try:
        # Input validation
        if not isinstance(name, str) or not name.strip():
            logger.error("Invalid name: must be non-empty string")
            return None

        if not isinstance(age, int) or age < 0 or age > 150:
            logger.error("Invalid age: must be integer between 0 and 150")
            return None

        if email is not None and (not isinstance(email, str) or '@' not in email):
            logger.error("Invalid email format")
            return None

        users = JSONDatabase.load_data(USERS_DB_PATH)

        # Check if user already exists by email
        if email:
            existing_user = next((u for u in users if u.get('email') == email), None)
            if existing_user:
                logger.info(f"User with email {email} already exists")
                return existing_user

        new_user = UserModel.create_user(name.strip(), age, email)

        # Validate the created user data
        if not JSONDatabase.validate_user_data(new_user):
            logger.error("Created user data failed validation")
            return None

        users.append(new_user)

        if JSONDatabase.save_data(USERS_DB_PATH, users):
            # Create initial streak record
            create_streak(new_user['id'])
            logger.info(f"Successfully created user: {new_user['name']} (ID: {new_user['id']})")
            return new_user
        else:
            logger.error("Failed to save user data")
            return None

    except Exception as e:
        logger.error(f"Error creating user: {e}")
        return None

def read_user(user_id: str) -> Optional[Dict[str, Any]]:
    """Read user by ID"""
    users = JSONDatabase.load_data(USERS_DB_PATH)
    return next((u for u in users if u['id'] == user_id), None)

def read_user_by_email(email: str) -> Optional[Dict[str, Any]]:
    """Read user by email"""
    users = JSONDatabase.load_data(USERS_DB_PATH)
    return next((u for u in users if u.get('email') == email), None)

def update_user(user_id: str, updates: Dict[str, Any]) -> bool:
    """Update user data with validation"""
    try:
        if not isinstance(user_id, str) or not user_id.strip():
            logger.error("Invalid user_id: must be non-empty string")
            return False

        if not isinstance(updates, dict):
            logger.error("Invalid updates: must be dictionary")
            return False

        users = JSONDatabase.load_data(USERS_DB_PATH)

        for i, user in enumerate(users):
            if user['id'] == user_id:
                # Create a copy to validate before applying changes
                updated_user = user.copy()
                updated_user.update(updates)
                updated_user['updated_at'] = current_timestamp()

                # Validate the updated user data
                if not JSONDatabase.validate_user_data(updated_user):
                    logger.error(f"Updated user data failed validation for user {user_id}")
                    return False

                users[i] = updated_user

                if JSONDatabase.save_data(USERS_DB_PATH, users):
                    logger.info(f"Successfully updated user {user_id}")
                    return True
                else:
                    logger.error(f"Failed to save updated user data for {user_id}")
                    return False

        logger.warning(f"User {user_id} not found for update")
        return False

    except Exception as e:
        logger.error(f"Error updating user {user_id}: {e}")
        return False

def delete_user(user_id: str) -> bool:
    """Delete user (soft delete by marking as inactive)"""
    return update_user(user_id, {'active': False, 'deleted_at': current_timestamp()})

def list_users() -> List[Dict[str, Any]]:
    """List all active users"""
    users = JSONDatabase.load_data(USERS_DB_PATH)
    return [u for u in users if u.get('active', True)]

# Session CRUD Operations
def create_session(user_id: str, session_type: str = 'chat') -> Dict[str, Any]:
    """Create a new chat session"""
    sessions = JSONDatabase.load_data(SESSIONS_DB_PATH)
    
    new_session = SessionModel.create_session(user_id, session_type)
    sessions.append(new_session)
    
    if JSONDatabase.save_data(SESSIONS_DB_PATH, sessions):
        return new_session
    return None

def read_session(session_id: str) -> Optional[Dict[str, Any]]:
    """Read session by ID"""
    sessions = JSONDatabase.load_data(SESSIONS_DB_PATH)
    return next((s for s in sessions if s['id'] == session_id), None)

def update_session(session_id: str, updates: Dict[str, Any]) -> bool:
    """Update session data"""
    sessions = JSONDatabase.load_data(SESSIONS_DB_PATH)
    
    for i, session in enumerate(sessions):
        if session['id'] == session_id:
            session.update(updates)
            sessions[i] = session
            return JSONDatabase.save_data(SESSIONS_DB_PATH, sessions)
    
    return False

def get_user_sessions(user_id: str) -> List[Dict[str, Any]]:
    """Get all sessions for a user"""
    sessions = JSONDatabase.load_data(SESSIONS_DB_PATH)
    return [s for s in sessions if s['user_id'] == user_id]

# Streak CRUD Operations
def create_streak(user_id: str) -> Dict[str, Any]:
    """Create streak record for user"""
    streaks = JSONDatabase.load_data(STREAKS_DB_PATH)
    
    # Check if streak already exists
    existing_streak = next((s for s in streaks if s['user_id'] == user_id), None)
    if existing_streak:
        return existing_streak
    
    new_streak = StreakModel.create_streak(user_id)
    streaks.append(new_streak)
    
    if JSONDatabase.save_data(STREAKS_DB_PATH, streaks):
        return new_streak
    return None

def read_streak(user_id: str) -> Optional[Dict[str, Any]]:
    """Read streak data for user"""
    streaks = JSONDatabase.load_data(STREAKS_DB_PATH)
    return next((s for s in streaks if s['user_id'] == user_id), None)

def update_streak(user_id: str, updates: Dict[str, Any]) -> bool:
    """Update streak data"""
    streaks = JSONDatabase.load_data(STREAKS_DB_PATH)
    
    for i, streak in enumerate(streaks):
        if streak['user_id'] == user_id:
            streak.update(updates)
            streaks[i] = streak
            return JSONDatabase.save_data(STREAKS_DB_PATH, streaks)
    
    return False

# Utility functions
def add_cvd_score_to_user(user_id: str, score_data: Dict[str, Any]) -> bool:
    """Add CVD score to user's history"""
    user = read_user(user_id)
    if not user:
        return False
    
    if 'cvd_scores' not in user:
        user['cvd_scores'] = []
    
    user['cvd_scores'].append(score_data)
    return update_user(user_id, {'cvd_scores': user['cvd_scores']})

def add_nutrition_log_to_user(user_id: str, nutrition_data: Dict[str, Any]) -> bool:
    """Add nutrition log to user's history"""
    try:
        user = read_user(user_id)
        if not user:
            logger.error(f"User {user_id} not found for nutrition log")
            return False

        if 'nutrition_logs' not in user:
            user['nutrition_logs'] = []

        # Add timestamp if not present
        if 'logged_at' not in nutrition_data:
            nutrition_data['logged_at'] = current_timestamp()

        user['nutrition_logs'].append(nutrition_data)
        return update_user(user_id, {'nutrition_logs': user['nutrition_logs']})

    except Exception as e:
        logger.error(f"Error adding nutrition log to user {user_id}: {e}")
        return False

# Database Management Utilities
def backup_database() -> bool:
    """Create backup of all database files"""
    try:
        backup_dir = os.path.join(DATA_DIR, 'backups')
        os.makedirs(backup_dir, exist_ok=True)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        for db_name, db_path in [
            ('users', USERS_DB_PATH),
            ('sessions', SESSIONS_DB_PATH),
            ('streaks', STREAKS_DB_PATH)
        ]:
            if os.path.exists(db_path):
                backup_path = os.path.join(backup_dir, f"{db_name}_{timestamp}.json")
                shutil.copy2(db_path, backup_path)
                logger.info(f"Backed up {db_name} to {backup_path}")

        return True

    except Exception as e:
        logger.error(f"Error creating database backup: {e}")
        return False

def restore_database(backup_timestamp: str) -> bool:
    """Restore database from backup"""
    try:
        backup_dir = os.path.join(DATA_DIR, 'backups')

        for db_name, db_path in [
            ('users', USERS_DB_PATH),
            ('sessions', SESSIONS_DB_PATH),
            ('streaks', STREAKS_DB_PATH)
        ]:
            backup_path = os.path.join(backup_dir, f"{db_name}_{backup_timestamp}.json")
            if os.path.exists(backup_path):
                shutil.copy2(backup_path, db_path)
                logger.info(f"Restored {db_name} from {backup_path}")
            else:
                logger.warning(f"Backup file not found: {backup_path}")

        return True

    except Exception as e:
        logger.error(f"Error restoring database: {e}")
        return False

def get_database_health() -> Dict[str, Any]:
    """Get comprehensive database health information"""
    try:
        health_info = {
            'status': 'healthy',
            'stats': JSONDatabase.get_database_stats(),
            'issues': [],
            'recommendations': []
        }

        # Check for issues
        for db_name, stats in health_info['stats'].items():
            if stats['record_count'] == 0 and db_name == 'users':
                health_info['issues'].append("No users in database")
                health_info['status'] = 'warning'

            if stats['file_size'] > 10 * 1024 * 1024:  # 10MB
                health_info['issues'].append(f"{db_name} database is large ({stats['file_size']} bytes)")
                health_info['recommendations'].append(f"Consider archiving old {db_name} data")

        # Check backup status
        backup_dir = os.path.join(DATA_DIR, 'backups')
        if not os.path.exists(backup_dir):
            health_info['issues'].append("No backup directory found")
            health_info['recommendations'].append("Create database backups regularly")

        if health_info['issues']:
            health_info['status'] = 'warning' if health_info['status'] == 'healthy' else health_info['status']

        return health_info

    except Exception as e:
        logger.error(f"Error checking database health: {e}")
        return {
            'status': 'error',
            'error': str(e),
            'stats': {},
            'issues': ['Failed to check database health'],
            'recommendations': ['Check database file permissions and disk space']
        }

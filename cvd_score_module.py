"""
CVD Risk Score Calculation Module for MIT CVD App
Simplified cardiovascular risk assessment based on user data and lifestyle factors
"""
import math
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

from config import CVD_RISK_WEIGHTS
from data_models import CVDScoreModel, current_timestamp

class CVDRiskCalculator:
    """Cardiovascular Disease Risk Assessment Calculator"""
    
    def __init__(self):
        # Risk factor weights (can be customized)
        self.weights = CVD_RISK_WEIGHTS
        
        # Age risk multipliers
        self.age_risk_multipliers = {
            (0, 30): 0.1,
            (30, 40): 0.3,
            (40, 50): 0.5,
            (50, 60): 0.7,
            (60, 70): 0.9,
            (70, 100): 1.0
        }
        
        # Family history risk factors
        self.family_history_risks = {
            'heart_attack': 0.8,
            'stroke': 0.6,
            'high_blood_pressure': 0.4,
            'diabetes': 0.5,
            'high_cholesterol': 0.3
        }
    
    def calculate_cvd_risk(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calculate comprehensive CVD risk score
        
        Args:
            user_data: User profile data including age, lifestyle, family history
            
        Returns:
            Dict with risk score, level, factors, and recommendations
        """
        try:
            # Extract user information
            age = user_data.get('age', 30)
            profile = user_data.get('profile', {})
            lifestyle = profile.get('lifestyle', {})
            family_history = profile.get('family_history', {})
            nutrition_logs = user_data.get('nutrition_logs', [])
            
            # Calculate individual risk components
            age_risk = self._calculate_age_risk(age)
            family_risk = self._calculate_family_history_risk(family_history)
            lifestyle_risk = self._calculate_lifestyle_risk(lifestyle)
            nutrition_risk = self._calculate_nutrition_risk(nutrition_logs)
            
            # Combine risks using weighted formula
            total_risk = (
                age_risk * self.weights['age'] +
                family_risk * self.weights['family_history'] +
                lifestyle_risk * 0.45 +  # Combined lifestyle factors
                nutrition_risk * self.weights['diet_score']
            )
            
            # Normalize to 0-1 scale
            normalized_risk = min(1.0, max(0.0, total_risk))
            
            # Generate detailed analysis
            risk_factors = self._identify_risk_factors(user_data, {
                'age_risk': age_risk,
                'family_risk': family_risk,
                'lifestyle_risk': lifestyle_risk,
                'nutrition_risk': nutrition_risk
            })
            
            protective_factors = self._identify_protective_factors(user_data)
            recommendations = self._generate_recommendations(risk_factors, user_data)
            
            # Create score record
            score_data = CVDScoreModel.create_score(
                user_data.get('id', 'unknown'),
                normalized_risk,
                {
                    'age_risk': age_risk,
                    'family_risk': family_risk,
                    'lifestyle_risk': lifestyle_risk,
                    'nutrition_risk': nutrition_risk,
                    'risk_factors': risk_factors,
                    'protective_factors': protective_factors
                }
            )
            
            return {
                'success': True,
                'score': normalized_risk,
                'risk_level': CVDScoreModel.get_risk_level(normalized_risk),
                'percentage': round(normalized_risk * 100, 1),
                'risk_factors': risk_factors,
                'protective_factors': protective_factors,
                'recommendations': recommendations,
                'component_scores': {
                    'age': age_risk,
                    'family_history': family_risk,
                    'lifestyle': lifestyle_risk,
                    'nutrition': nutrition_risk
                },
                'score_data': score_data,
                'calculation_timestamp': current_timestamp()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"CVD risk calculation error: {str(e)}",
                'score': 0.5,  # Default moderate risk
                'risk_level': 'Unknown'
            }
    
    def _calculate_age_risk(self, age: int) -> float:
        """Calculate age-based risk factor"""
        for (min_age, max_age), multiplier in self.age_risk_multipliers.items():
            if min_age <= age < max_age:
                return multiplier
        return 1.0  # Default for very old age
    
    def _calculate_family_history_risk(self, family_history: Dict[str, Any]) -> float:
        """Calculate family history risk factor"""
        if not family_history:
            return 0.0
        
        total_risk = 0.0
        for condition, risk_value in self.family_history_risks.items():
            if family_history.get(condition, False):
                total_risk += risk_value
        
        return min(1.0, total_risk)
    
    def _calculate_lifestyle_risk(self, lifestyle: Dict[str, Any]) -> float:
        """Calculate lifestyle-based risk factors"""
        risk_score = 0.0
        
        # Smoking (highest risk factor)
        if lifestyle.get('smoking', False):
            risk_score += 0.4
        
        # Exercise frequency (protective when high)
        exercise_freq = lifestyle.get('exercise_frequency', 0)
        if exercise_freq == 0:
            risk_score += 0.3
        elif exercise_freq < 3:
            risk_score += 0.15
        # 3+ times per week is protective (no added risk)
        
        # Stress level (1-10 scale)
        stress_level = lifestyle.get('stress_level', 5)
        if stress_level > 7:
            risk_score += 0.2
        elif stress_level > 5:
            risk_score += 0.1
        
        # Sleep hours
        sleep_hours = lifestyle.get('sleep_hours', 8)
        if sleep_hours < 6 or sleep_hours > 9:
            risk_score += 0.1
        
        return min(1.0, risk_score)
    
    def _calculate_nutrition_risk(self, nutrition_logs: List[Dict[str, Any]]) -> float:
        """Calculate nutrition-based risk from recent food logs"""
        if not nutrition_logs:
            return 0.5  # Default moderate risk if no data
        
        # Analyze recent nutrition logs (last 7 days)
        recent_logs = self._get_recent_nutrition_logs(nutrition_logs, days=7)
        
        if not recent_logs:
            return 0.5
        
        total_risk = 0.0
        log_count = len(recent_logs)
        
        for log in recent_logs:
            nutrition = log.get('nutrition', {})
            
            # High sodium risk
            sodium = nutrition.get('sodium', 0)
            if sodium > 600:  # mg per serving
                total_risk += 0.3
            elif sodium > 400:
                total_risk += 0.15
            
            # Saturated fat risk
            sat_fat = nutrition.get('saturated_fat', 0)
            if sat_fat > 5:  # g per serving
                total_risk += 0.25
            elif sat_fat > 3:
                total_risk += 0.1
            
            # Trans fat risk
            if nutrition.get('trans_fat', 0) > 0:
                total_risk += 0.4
            
            # Low fiber (lack of protective factor)
            if nutrition.get('fiber', 0) < 3:
                total_risk += 0.1
        
        return min(1.0, total_risk / log_count)
    
    def _get_recent_nutrition_logs(self, nutrition_logs: List[Dict[str, Any]], days: int = 7) -> List[Dict[str, Any]]:
        """Get nutrition logs from the last N days"""
        cutoff_date = datetime.now() - timedelta(days=days)
        recent_logs = []
        
        for log in nutrition_logs:
            try:
                log_date = datetime.fromisoformat(log.get('logged_at', ''))
                if log_date >= cutoff_date:
                    recent_logs.append(log)
            except:
                continue  # Skip logs with invalid dates
        
        return recent_logs
    
    def _identify_risk_factors(self, user_data: Dict[str, Any], component_scores: Dict[str, float]) -> List[str]:
        """Identify specific risk factors present"""
        risk_factors = []
        
        age = user_data.get('age', 30)
        profile = user_data.get('profile', {})
        lifestyle = profile.get('lifestyle', {})
        family_history = profile.get('family_history', {})
        
        # Age-related risks
        if age >= 50:
            risk_factors.append(f"Age {age} (increased risk)")
        
        # Family history risks
        for condition in self.family_history_risks:
            if family_history.get(condition, False):
                risk_factors.append(f"Family history of {condition.replace('_', ' ')}")
        
        # Lifestyle risks
        if lifestyle.get('smoking', False):
            risk_factors.append("Current smoker")
        
        if lifestyle.get('exercise_frequency', 0) == 0:
            risk_factors.append("Sedentary lifestyle")
        
        if lifestyle.get('stress_level', 5) > 7:
            risk_factors.append("High stress levels")
        
        sleep_hours = lifestyle.get('sleep_hours', 8)
        if sleep_hours < 6:
            risk_factors.append("Insufficient sleep")
        elif sleep_hours > 9:
            risk_factors.append("Excessive sleep")
        
        # Nutrition risks (from recent analysis)
        if component_scores.get('nutrition_risk', 0) > 0.6:
            risk_factors.append("Poor dietary patterns")
        
        return risk_factors
    
    def _identify_protective_factors(self, user_data: Dict[str, Any]) -> List[str]:
        """Identify protective factors present"""
        protective_factors = []
        
        profile = user_data.get('profile', {})
        lifestyle = profile.get('lifestyle', {})
        
        # Exercise
        exercise_freq = lifestyle.get('exercise_frequency', 0)
        if exercise_freq >= 5:
            protective_factors.append("Very active lifestyle (5+ workouts/week)")
        elif exercise_freq >= 3:
            protective_factors.append("Active lifestyle (3+ workouts/week)")
        
        # Non-smoking
        if not lifestyle.get('smoking', False):
            protective_factors.append("Non-smoker")
        
        # Good sleep
        sleep_hours = lifestyle.get('sleep_hours', 8)
        if 7 <= sleep_hours <= 8:
            protective_factors.append("Healthy sleep patterns")
        
        # Low stress
        if lifestyle.get('stress_level', 5) <= 3:
            protective_factors.append("Low stress levels")
        
        # Young age
        age = user_data.get('age', 30)
        if age < 40:
            protective_factors.append("Young age")
        
        return protective_factors
    
    def _generate_recommendations(self, risk_factors: List[str], user_data: Dict[str, Any]) -> List[str]:
        """Generate personalized recommendations based on risk factors"""
        recommendations = []
        
        profile = user_data.get('profile', {})
        lifestyle = profile.get('lifestyle', {})
        
        # Exercise recommendations
        exercise_freq = lifestyle.get('exercise_frequency', 0)
        if exercise_freq == 0:
            recommendations.append("Start with 30 minutes of moderate exercise 3 times per week")
        elif exercise_freq < 3:
            recommendations.append("Increase exercise frequency to at least 3 times per week")
        
        # Smoking cessation
        if lifestyle.get('smoking', False):
            recommendations.append("Consider smoking cessation programs - this is the most impactful change you can make")
        
        # Stress management
        if lifestyle.get('stress_level', 5) > 7:
            recommendations.append("Practice stress reduction techniques like meditation or yoga")
        
        # Sleep optimization
        sleep_hours = lifestyle.get('sleep_hours', 8)
        if sleep_hours < 7:
            recommendations.append("Aim for 7-8 hours of sleep per night")
        elif sleep_hours > 9:
            recommendations.append("Consider evaluating sleep quality - excessive sleep may indicate underlying issues")
        
        # Diet recommendations
        recommendations.append("Focus on heart-healthy foods: fruits, vegetables, whole grains, lean proteins")
        recommendations.append("Limit sodium intake to less than 2,300mg per day")
        
        # Medical follow-up
        age = user_data.get('age', 30)
        if age >= 40 or len(risk_factors) > 2:
            recommendations.append("Schedule regular check-ups with your healthcare provider")
        
        return recommendations

# Convenience functions
def calculate_cvd_risk(user_data: Dict[str, Any]) -> Dict[str, Any]:
    """Main CVD risk calculation function - wrapper for the class method"""
    calculator = CVDRiskCalculator()
    return calculator.calculate_cvd_risk(user_data)

def quick_risk_assessment(age: int, smoking: bool = False, exercise_freq: int = 0, family_history: bool = False) -> Dict[str, Any]:
    """Quick risk assessment with minimal data"""
    user_data = {
        'age': age,
        'profile': {
            'lifestyle': {
                'smoking': smoking,
                'exercise_frequency': exercise_freq,
                'stress_level': 5,
                'sleep_hours': 8
            },
            'family_history': {
                'heart_attack': family_history,
                'high_blood_pressure': family_history
            }
        }
    }
    
    return calculate_cvd_risk(user_data)

def get_risk_trend(user_id: str, cvd_scores: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze risk trend over time"""
    if len(cvd_scores) < 2:
        return {'trend': 'insufficient_data', 'message': 'Need more data points to determine trend'}
    
    # Sort by date
    sorted_scores = sorted(cvd_scores, key=lambda x: x.get('calculated_at', ''))
    
    recent_score = sorted_scores[-1]['score']
    previous_score = sorted_scores[-2]['score']
    
    change = recent_score - previous_score
    
    if abs(change) < 0.05:
        trend = 'stable'
        message = 'Your CVD risk has remained stable'
    elif change > 0:
        trend = 'increasing'
        message = f'Your CVD risk has increased by {abs(change)*100:.1f}%'
    else:
        trend = 'decreasing'
        message = f'Your CVD risk has decreased by {abs(change)*100:.1f}%'
    
    return {
        'trend': trend,
        'change': change,
        'message': message,
        'current_score': recent_score,
        'previous_score': previous_score
    }

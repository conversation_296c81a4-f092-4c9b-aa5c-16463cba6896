"""
Data models for MIT CVD App
Simple data structures for JSON storage
"""
from datetime import datetime
from typing import Dict, List, Optional, Any
import uuid

def generate_id() -> str:
    """Generate a unique ID for records"""
    return str(uuid.uuid4())

def current_timestamp() -> str:
    """Get current timestamp as ISO string"""
    return datetime.now().isoformat()

class UserModel:
    """User data model"""
    
    @staticmethod
    def create_user(name: str, age: int, email: str = None) -> Dict[str, Any]:
        return {
            'id': generate_id(),
            'name': name,
            'age': age,
            'email': email,
            'created_at': current_timestamp(),
            'updated_at': current_timestamp(),
            'profile': {
                'family_history': {},
                'medical_conditions': [],
                'medications': [],
                'lifestyle': {
                    'smoking': False,
                    'exercise_frequency': 0,  # times per week
                    'stress_level': 5,  # 1-10 scale
                    'sleep_hours': 8
                }
            },
            'cvd_scores': [],  # Historical scores
            'nutrition_logs': [],
            'chat_sessions': []
        }

class SessionModel:
    """Chat session data model"""
    
    @staticmethod
    def create_session(user_id: str, session_type: str = 'chat') -> Dict[str, Any]:
        return {
            'id': generate_id(),
            'user_id': user_id,
            'type': session_type,  # 'chat', 'audio', 'image'
            'created_at': current_timestamp(),
            'messages': [],
            'insights': [],
            'cvd_score': None
        }

class CVDScoreModel:
    """CVD Risk Score data model"""
    
    @staticmethod
    def create_score(user_id: str, score: float, factors: Dict[str, Any]) -> Dict[str, Any]:
        return {
            'id': generate_id(),
            'user_id': user_id,
            'score': score,
            'risk_level': CVDScoreModel.get_risk_level(score),
            'factors': factors,
            'calculated_at': current_timestamp()
        }
    
    @staticmethod
    def get_risk_level(score: float) -> str:
        """Convert numeric score to risk level"""
        if score < 0.3:
            return 'Low'
        elif score < 0.6:
            return 'Moderate'
        elif score < 0.8:
            return 'High'
        else:
            return 'Very High'

class NutritionLogModel:
    """Nutrition log data model"""
    
    @staticmethod
    def create_log(user_id: str, food_data: Dict[str, Any]) -> Dict[str, Any]:
        return {
            'id': generate_id(),
            'user_id': user_id,
            'food_name': food_data.get('name', 'Unknown'),
            'nutrition': food_data.get('nutrition', {}),
            'image_path': food_data.get('image_path'),
            'logged_at': current_timestamp()
        }

class StreakModel:
    """Gamification streak data model"""
    
    @staticmethod
    def create_streak(user_id: str) -> Dict[str, Any]:
        return {
            'user_id': user_id,
            'current_streak': 0,
            'longest_streak': 0,
            'total_checkins': 0,
            'last_checkin': None,
            'points': 0,
            'achievements': []
        }
